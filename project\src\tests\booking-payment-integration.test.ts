/**
 * Comprehensive Booking and Payment Integration Tests
 * 
 * Tests the complete booking and payment flow:
 * - Form validation and submission
 * - Booking creation and status management
 * - Payment processing and status updates
 * - Error handling and recovery
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { User } from '@supabase/supabase-js';

// Mock Supabase first
vi.mock('../lib/supabase/client', () => ({
  supabase: {
    from: vi.fn()
  }
}));

// Now import the services
import { BookingService, StandardizedBookingData } from '../lib/api/bookingService';
import { EnhancedPaymentService } from '../lib/api/enhancedPaymentService';
import { BookingManagementService } from '../lib/api/bookingManagementService';
import { supabase } from '../lib/supabase/client';

const mockBookingData: StandardizedBookingData = {
  id: 'booking-123',
  user_id: 'user-123',
  service_type: 'residential_regular',
  property_details: {
    propertyType: 'house',
    propertySize: 'medium',
    address: '123 Main St',
    city: 'Anytown',
    zipCode: '12345'
  },
  service_details: {
    frequency: 'weekly',
    addOns: ['deep-clean']
  },
  schedule: {
    preferredDate: '2025-12-31',
    preferredTime: 'morning'
  },
  contact: {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '5551234567'
  },
  status: 'pending',
  payment_status: 'pending',
  total_price: 150,
  metadata: {
    submittedAt: new Date().toISOString(),
    requestType: 'booking'
  },
  created_at: new Date().toISOString()
};



// Mock fetch for payment API
global.fetch = vi.fn().mockResolvedValue({
  ok: true,
  json: vi.fn().mockResolvedValue({
    success: true,
    paymentLink: {
      id: 'payment-link-123',
      url: 'https://square.link/payment/123'
    }
  })
});

describe('Complete Booking and Payment Flow', () => {
  const mockUser: User = {
    id: 'user-123',
    email: '<EMAIL>',
    created_at: new Date().toISOString(),
    app_metadata: {},
    user_metadata: {},
    aud: 'authenticated',
    role: 'authenticated'
  };

  const validFormData = {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '5551234567',
    address: '123 Main St',
    city: 'Anytown',
    zipCode: '12345',
    propertyType: 'house',
    propertySize: 'medium',
    preferredDate: '2025-12-31',
    preferredTime: 'morning',
    addOns: ['deep-clean'],
    specialInstructions: 'Please be careful with antique furniture'
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup successful booking save mock
    (supabase.from as any).mockReturnValue({
      insert: vi.fn().mockReturnValue({
        select: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({
            data: mockBookingData,
            error: null
          })
        })
      }),
      update: vi.fn().mockReturnValue({
        eq: vi.fn().mockResolvedValue({
          data: null,
          error: null
        })
      }),
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({
            data: mockBookingData,
            error: null
          })
        })
      })
    });

    // Setup payment record mock
    (supabase.from as any).mockImplementation((table: string) => {
      if (table === 'payment_records') {
        return {
          insert: vi.fn().mockReturnValue({
            select: vi.fn().mockReturnValue({
              single: vi.fn().mockResolvedValue({
                data: {
                  id: 'payment-record-123',
                  booking_id: 'booking-123',
                  amount: 150,
                  currency: 'USD',
                  status: 'pending',
                  customer_email: '<EMAIL>',
                  description: 'residential_regular - house',
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString()
                },
                error: null
              })
            })
          }),
          update: vi.fn().mockReturnValue({
            eq: vi.fn().mockResolvedValue({
              data: null,
              error: null
            })
          }),
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              order: vi.fn().mockReturnValue({
                limit: vi.fn().mockReturnValue({
                  single: vi.fn().mockResolvedValue({
                    data: {
                      id: 'payment-record-123',
                      booking_id: 'booking-123',
                      payment_link_id: 'payment-link-123',
                      status: 'pending'
                    },
                    error: null
                  })
                })
              })
            })
          })
        };
      }

      // Default booking_forms table mock
      return {
        insert: vi.fn().mockReturnValue({
          select: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: mockBookingData,
              error: null
            })
          })
        }),
        update: vi.fn().mockReturnValue({
          eq: vi.fn().mockResolvedValue({
            data: null,
            error: null
          })
        }),
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: mockBookingData,
              error: null
            })
          })
        })
      };
    });
  });

  describe('End-to-End Booking Flow', () => {
    it('should complete full booking and payment flow successfully', async () => {
      // Step 1: Validate form data
      const validation = BookingService.validateBookingData(
        validFormData,
        'residential_regular',
        mockUser
      );

      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
      expect(validation.sanitizedData).toBeDefined();

      // Step 2: Save booking
      const booking = await BookingService.saveBooking(
        validFormData,
        'residential_regular',
        mockUser
      );

      expect(booking).toBeDefined();
      expect(booking.id).toBe('booking-123');
      expect(booking.status).toBe('pending');
      expect(booking.payment_status).toBe('pending');

      // Step 3: Create payment
      const paymentResult = await EnhancedPaymentService.createPaymentForBooking(
        booking,
        150,
        mockUser
      );

      expect(paymentResult.success).toBe(true);
      expect(paymentResult.paymentUrl).toBe('https://square.link/payment/123');
      expect(paymentResult.paymentLinkId).toBe('payment-link-123');

      // Step 4: Simulate payment completion
      await EnhancedPaymentService.handlePaymentStatusUpdate(
        'payment-link-123',
        'completed',
        'square-payment-123'
      );

      // Verify booking status was updated
      expect(supabase.from).toHaveBeenCalledWith('booking_forms');
    });

    it('should handle validation errors properly', async () => {
      const invalidFormData = {
        firstName: '',
        lastName: 'Doe',
        email: 'invalid-email',
        phone: '123',
        address: '',
        city: '',
        zipCode: '',
        propertyType: '',
        propertySize: '',
        preferredDate: '2020-01-01', // Past date
        preferredTime: ''
      };

      const validation = BookingService.validateBookingData(
        invalidFormData,
        'residential_regular',
        mockUser
      );

      expect(validation.isValid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);

      // Check specific validation errors
      const errorFields = validation.errors.map(err => err.field);
      expect(errorFields).toContain('firstName');
      expect(errorFields).toContain('email');
      expect(errorFields).toContain('phone');
      expect(errorFields).toContain('address');
      expect(errorFields).toContain('city');
      expect(errorFields).toContain('zipCode');
      expect(errorFields).toContain('propertyType');
      expect(errorFields).toContain('propertySize');
      expect(errorFields).toContain('preferredDate');
      expect(errorFields).toContain('preferredTime');
    });

    it('should handle payment failures gracefully', async () => {
      // Mock payment API failure
      global.fetch = vi.fn().mockResolvedValue({
        ok: false,
        statusText: 'Payment Service Unavailable'
      });

      const booking = await BookingService.saveBooking(
        validFormData,
        'residential_regular',
        mockUser
      );

      const paymentResult = await EnhancedPaymentService.createPaymentForBooking(
        booking,
        150,
        mockUser
      );

      expect(paymentResult.success).toBe(false);
      expect(paymentResult.error).toBeDefined();
    });

    it('should handle booking status transitions correctly', async () => {
      const booking = await BookingService.saveBooking(
        validFormData,
        'residential_regular',
        mockUser
      );

      // Test valid status transition
      await BookingManagementService.updateBookingStatus(
        booking.id!,
        'confirmed',
        'Payment completed',
        'system',
        'paid'
      );

      expect(supabase.from).toHaveBeenCalledWith('booking_forms');

      // Test invalid status transition
      (supabase.from as any).mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: { ...mockBookingData, status: 'completed' },
              error: null
            })
          })
        })
      });

      await expect(
        BookingManagementService.updateBookingStatus(
          booking.id!,
          'pending',
          'Invalid transition'
        )
      ).rejects.toThrow('Invalid status transition');
    });

    it('should sanitize malicious input properly', async () => {
      const maliciousFormData = {
        ...validFormData,
        firstName: '<script>alert("xss")</script>John',
        lastName: 'Doe<img src=x onerror=alert(1)>',
        specialInstructions: 'Clean <script>document.cookie</script> thoroughly',
        address: '123 Main St<iframe src="javascript:alert(1)"></iframe>'
      };

      const validation = BookingService.validateBookingData(
        maliciousFormData,
        'residential_regular',
        mockUser
      );

      expect(validation.isValid).toBe(true);
      expect(validation.sanitizedData?.contact.firstName).not.toContain('<script>');
      expect(validation.sanitizedData?.contact.lastName).not.toContain('<img');
      expect(validation.sanitizedData?.special_instructions).not.toContain('<script>');
      expect(validation.sanitizedData?.property_details.address).not.toContain('<iframe>');
    });

    it('should handle different service types correctly', async () => {
      const serviceTypes = [
        'residential_regular',
        'residential_deep',
        'pool',
        'carpet',
        'office',
        'construction',
        'waste-management'
      ];

      for (const serviceType of serviceTypes) {
        const validation = BookingService.validateBookingData(
          validFormData,
          serviceType,
          mockUser
        );

        expect(validation.isValid).toBe(true);
        expect(validation.sanitizedData?.service_type).toBe(serviceType);

        const serviceDetails = BookingService.getServiceSpecificDetails(
          validFormData,
          serviceType
        );

        expect(serviceDetails).toBeDefined();
        expect(typeof serviceDetails).toBe('object');
      }
    });
  });

  describe('Error Recovery and Edge Cases', () => {
    it('should handle database connection failures', async () => {
      (supabase.from as any).mockReturnValue({
        insert: vi.fn().mockReturnValue({
          select: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: null,
              error: { message: 'Database connection failed' }
            })
          })
        })
      });

      await expect(
        BookingService.saveBooking(validFormData, 'residential_regular', mockUser)
      ).rejects.toThrow('Failed to save booking');
    });

    it('should handle missing user authentication', async () => {
      const paymentResult = await EnhancedPaymentService.createPaymentForBooking(
        mockBookingData,
        150,
        null
      );

      expect(paymentResult.success).toBe(false);
      expect(paymentResult.error).toContain('User authentication required');
    });

    it('should validate payment amounts', async () => {
      const invalidAmounts = [-50, 0, 15000];

      for (const amount of invalidAmounts) {
        const paymentResult = await EnhancedPaymentService.createPaymentForBooking(
          mockBookingData,
          amount,
          mockUser
        );

        expect(paymentResult.success).toBe(false);
        expect(paymentResult.error).toContain('Invalid payment amount');
      }
    });
  });
});
