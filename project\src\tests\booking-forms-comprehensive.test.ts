/**
 * Comprehensive Booking Forms and Submission Tests
 * 
 * Tests the new standardized booking system including:
 * - BookingService validation and sanitization
 * - useBookingSubmission hook functionality
 * - Form validation and error handling
 * - Payment integration
 * - Status management
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { BookingService, StandardizedBookingData } from '../lib/api/bookingService';
import { useBookingSubmission, useFormValidation } from '../hooks/useBookingSubmission';
import { User } from '@supabase/supabase-js';

// Mock dependencies
vi.mock('../lib/supabase/client', () => ({
  supabase: {
    from: vi.fn().mockReturnValue({
      insert: vi.fn().mockReturnValue({
        select: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({
            data: {
              id: 'booking-123',
              user_id: 'user-123',
              service_type: 'residential_regular',
              status: 'pending',
              created_at: new Date().toISOString()
            },
            error: null
          })
        })
      }),
      update: vi.fn().mockReturnValue({
        eq: vi.fn().mockResolvedValue({
          data: null,
          error: null
        })
      }),
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          order: vi.fn().mockResolvedValue({
            data: [],
            error: null
          })
        })
      })
    })
  }
}));

vi.mock('../lib/api/paymentService', () => ({
  processResidentialPayment: vi.fn().mockResolvedValue({
    url: 'https://payment.example.com/pay/123',
    id: 'payment-123'
  })
}));

vi.mock('react-router-dom', () => ({
  useNavigate: () => vi.fn()
}));

describe('BookingService', () => {
  const mockUser: User = {
    id: 'user-123',
    email: '<EMAIL>',
    created_at: new Date().toISOString(),
    app_metadata: {},
    user_metadata: {},
    aud: 'authenticated',
    role: 'authenticated'
  };

  const validFormData = {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '5551234567',
    address: '123 Main St',
    city: 'Anytown',
    zipCode: '12345',
    propertyType: 'house',
    propertySize: 'medium',
    preferredDate: '2025-12-31', // Future date
    preferredTime: 'morning',
    addOns: ['deep-clean'],
    specialInstructions: 'Please be careful with antique furniture'
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('validateBookingData', () => {
    it('should validate valid form data successfully', () => {
      const result = BookingService.validateBookingData(
        validFormData,
        'residential_regular',
        mockUser
      );

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.sanitizedData).toBeDefined();
    });

    it('should return validation errors for missing required fields', () => {
      const invalidData = { ...validFormData };
      delete invalidData.firstName;
      delete invalidData.email;

      const result = BookingService.validateBookingData(
        invalidData,
        'residential_regular',
        mockUser
      );

      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors.some(err => err.field === 'firstName')).toBe(true);
      expect(result.errors.some(err => err.field === 'email')).toBe(true);
    });

    it('should sanitize input data properly', () => {
      const dataWithScripts = {
        ...validFormData,
        firstName: '<script>alert("xss")</script>John',
        specialInstructions: 'Clean <b>thoroughly</b> please'
      };

      const result = BookingService.validateBookingData(
        dataWithScripts,
        'residential_regular',
        mockUser
      );

      expect(result.sanitizedData?.contact.firstName).not.toContain('<script>');
      expect(result.sanitizedData?.special_instructions).not.toContain('<b>');
    });
  });

  describe('saveBooking', () => {
    it('should save booking successfully with valid data', async () => {
      const result = await BookingService.saveBooking(
        validFormData,
        'residential_regular',
        mockUser
      );

      expect(result).toBeDefined();
      expect(result.id).toBe('booking-123');
      expect(result.service_type).toBe('residential_regular');
      expect(result.status).toBe('pending');
    });

    it('should throw error for invalid data', async () => {
      const invalidData = { ...validFormData };
      delete invalidData.firstName;

      await expect(
        BookingService.saveBooking(invalidData, 'residential_regular', mockUser)
      ).rejects.toThrow('Validation failed');
    });
  });

  describe('getServiceSpecificDetails', () => {
    it('should extract residential service details correctly', () => {
      const details = BookingService.getServiceSpecificDetails(
        { ...validFormData, bedrooms: 3, bathrooms: 2, frequency: 'weekly' },
        'residential_regular'
      );

      expect(details.bedrooms).toBe(3);
      expect(details.bathrooms).toBe(2);
      expect(details.frequency).toBe('weekly');
    });

    it('should extract pool service details correctly', () => {
      const poolData = {
        ...validFormData,
        poolType: 'inground',
        poolSize: 'large',
        poolCondition: 'good'
      };

      const details = BookingService.getServiceSpecificDetails(poolData, 'pool');

      expect(details.poolType).toBe('inground');
      expect(details.poolSize).toBe('large');
      expect(details.poolCondition).toBe('good');
    });
  });
});

describe('useBookingSubmission Hook', () => {
  const mockUser: User = {
    id: 'user-123',
    email: '<EMAIL>',
    created_at: new Date().toISOString(),
    app_metadata: {},
    user_metadata: {},
    aud: 'authenticated',
    role: 'authenticated'
  };

  beforeEach(() => {
    vi.clearAllMocks();
    // Clear localStorage
    localStorage.clear();
  });

  it('should initialize with correct default state', () => {
    const { result } = renderHook(() =>
      useBookingSubmission('residential_regular', mockUser)
    );

    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBe(null);
    expect(result.current.success).toBe(false);
    expect(result.current.booking).toBe(null);
  });

  it('should handle successful booking submission', async () => {
    const { result } = renderHook(() =>
      useBookingSubmission('residential_regular', mockUser, {
        openPaymentModal: true
      })
    );

    const mockOnPaymentModalOpen = vi.fn();

    await act(async () => {
      await result.current.submitBooking(
        {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '555-0123',
          address: '123 Main St',
          city: 'Anytown',
          zipCode: '12345',
          propertyType: 'house',
          propertySize: 'medium',
          preferredDate: '2024-01-15',
          preferredTime: 'morning'
        },
        mockOnPaymentModalOpen
      );
    });

    expect(result.current.success).toBe(true);
    expect(result.current.booking).toBeDefined();
    expect(mockOnPaymentModalOpen).toHaveBeenCalled();
  });

  it('should save form data to localStorage when user not authenticated', async () => {
    const { result } = renderHook(() =>
      useBookingSubmission('residential_regular', null, {
        saveFormData: true,
        redirectToLogin: false
      })
    );

    const formData = { firstName: 'John', lastName: 'Doe' };

    await act(async () => {
      await result.current.submitBooking(formData);
    });

    const savedData = localStorage.getItem('residential_regularFormData');
    expect(savedData).toBe(JSON.stringify(formData));
  });

  it('should load saved form data from localStorage', () => {
    const formData = { firstName: 'John', lastName: 'Doe' };
    localStorage.setItem('residential_regularFormData', JSON.stringify(formData));

    const { result } = renderHook(() =>
      useBookingSubmission('residential_regular', mockUser)
    );

    const loadedData = result.current.loadSavedFormData();
    expect(loadedData).toEqual(formData);
  });
});

describe('useFormValidation Hook', () => {
  const mockUser: User = {
    id: 'user-123',
    email: '<EMAIL>',
    created_at: new Date().toISOString(),
    app_metadata: {},
    user_metadata: {},
    aud: 'authenticated',
    role: 'authenticated'
  };

  it('should validate individual fields correctly', () => {
    const { result } = renderHook(() =>
      useFormValidation('residential_regular', mockUser)
    );

    act(() => {
      const isValid = result.current.validateField(
        'firstName',
        'John',
        { firstName: 'John' }
      );
      expect(isValid).toBe(true);
    });
  });

  it('should track validation errors', () => {
    const { result } = renderHook(() =>
      useFormValidation('residential_regular', mockUser)
    );

    act(() => {
      result.current.validateField('firstName', '', { firstName: '' });
    });

    expect(result.current.errors.firstName).toBeDefined();
  });

  it('should clear field errors', () => {
    const { result } = renderHook(() =>
      useFormValidation('residential_regular', mockUser)
    );

    act(() => {
      result.current.validateField('firstName', '', { firstName: '' });
    });

    expect(result.current.errors.firstName).toBeDefined();

    act(() => {
      result.current.clearFieldError('firstName');
    });

    expect(result.current.errors.firstName).toBe('');
  });
});
