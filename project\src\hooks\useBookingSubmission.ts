import { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { User } from '@supabase/supabase-js';
import { BookingService, StandardizedBookingData } from '../lib/api/bookingService';
import { EnhancedPaymentService } from '../lib/api/enhancedPaymentService';
import { ErrorHandler } from '../lib/utils/errorHandler';

export interface BookingSubmissionState {
  isLoading: boolean;
  error: string | null;
  success: boolean;
  booking: StandardizedBookingData | null;
  paymentLink: string | null;
}

export interface BookingSubmissionOptions {
  saveFormData?: boolean;
  redirectToLogin?: boolean;
  openPaymentModal?: boolean;
  showSuccessPopup?: boolean;
}

/**
 * Standardized hook for booking form submission
 * Handles validation, saving, and payment processing consistently across all forms
 */
export function useBookingSubmission(
  serviceType: string,
  user: User | null,
  options: BookingSubmissionOptions = {}
) {
  const navigate = useNavigate();
  const [state, setState] = useState<BookingSubmissionState>({
    isLoading: false,
    error: null,
    success: false,
    booking: null,
    paymentLink: null
  });

  const {
    saveFormData = true,
    redirectToLogin = true,
    openPaymentModal = false,
    showSuccessPopup = false
  } = options;

  /**
   * Submit booking form with standardized flow
   */
  const submitBooking = async (
    formData: any,
    onPaymentModalOpen?: () => void,
    onSuccessPopup?: () => void
  ) => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Check authentication
      if (!user) {
        if (saveFormData) {
          const storageKey = `${serviceType}FormData`;
          localStorage.setItem(storageKey, JSON.stringify(formData));
          ErrorHandler.logInfo('Form data saved to localStorage', { serviceType, storageKey });
        }

        if (redirectToLogin) {
          navigate('/auth/login', { 
            state: { 
              from: window.location.pathname,
              message: 'Please log in to complete your booking'
            }
          });
          return;
        }
      }

      // Validate and save booking
      const booking = await BookingService.saveBooking(formData, serviceType, user);
      
      setState(prev => ({ 
        ...prev, 
        booking,
        success: true 
      }));

      // Clear saved form data on successful submission
      if (saveFormData) {
        const storageKey = `${serviceType}FormData`;
        localStorage.removeItem(storageKey);
      }

      // Handle post-submission actions
      if (showSuccessPopup && onSuccessPopup) {
        onSuccessPopup();
      } else if (openPaymentModal && onPaymentModalOpen) {
        onPaymentModalOpen();
      }

      ErrorHandler.logSuccess('Booking submitted successfully', {
        bookingId: booking.id,
        serviceType,
        userId: user?.id
      });

      return booking;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to submit booking';
      setState(prev => ({ 
        ...prev, 
        error: errorMessage 
      }));

      ErrorHandler.logError(ErrorHandler.createError(
        'BOOKING_SUBMISSION_ERROR',
        'Failed to submit booking form',
        error
      ));

      throw error;
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  };

  /**
   * Process payment for the booking using enhanced payment service
   */
  const processPayment = async (amount: number) => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      if (!user) {
        throw new Error('User must be authenticated to process payment');
      }

      if (!state.booking) {
        throw new Error('Booking must be created before processing payment');
      }

      const paymentResult = await EnhancedPaymentService.createPaymentForBooking(
        state.booking,
        amount,
        user
      );

      if (paymentResult.success && paymentResult.paymentUrl) {
        setState(prev => ({
          ...prev,
          paymentLink: paymentResult.paymentUrl
        }));

        // Store payment tracking data
        const trackingData = {
          paymentRecordId: paymentResult.paymentRecordId,
          paymentLinkId: paymentResult.paymentLinkId,
          bookingId: state.booking.id,
          startTime: Date.now(),
          isActive: true
        };
        localStorage.setItem('paymentTracking', JSON.stringify(trackingData));

        // Redirect to payment page
        window.location.href = paymentResult.paymentUrl;
      } else {
        throw new Error(paymentResult.error || 'Failed to create payment link');
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Payment processing failed';
      setState(prev => ({
        ...prev,
        error: errorMessage
      }));

      ErrorHandler.logError(ErrorHandler.createError(
        'PAYMENT_PROCESSING_ERROR',
        'Failed to process payment',
        error
      ));

      throw error;
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  };

  /**
   * Reset submission state
   */
  const resetState = () => {
    setState({
      isLoading: false,
      error: null,
      success: false,
      booking: null,
      paymentLink: null
    });
  };

  /**
   * Load saved form data from localStorage
   */
  const loadSavedFormData = () => {
    try {
      const storageKey = `${serviceType}FormData`;
      const savedData = localStorage.getItem(storageKey);
      return savedData ? JSON.parse(savedData) : null;
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.createError(
        'FORM_DATA_LOAD_ERROR',
        'Failed to load saved form data',
        error
      ));
      return null;
    }
  };

  /**
   * Validate form data before submission
   */
  const validateForm = (formData: any) => {
    return BookingService.validateBookingData(formData, serviceType, user);
  };

  return {
    state,
    submitBooking,
    processPayment,
    resetState,
    loadSavedFormData,
    validateForm,
    isLoading: state.isLoading,
    error: state.error,
    success: state.success,
    booking: state.booking,
    paymentLink: state.paymentLink
  };
}

/**
 * Hook for enhanced form validation with real-time feedback
 */
export function useFormValidation(serviceType: string, user: User | null) {
  const [validationState, setValidationState] = useState<{
    errors: Record<string, string>;
    warnings: Record<string, string>;
    isValid: boolean;
    touched: Record<string, boolean>;
    isValidating: boolean;
  }>({
    errors: {},
    warnings: {},
    isValid: false,
    touched: {},
    isValidating: false
  });

  const validateField = useCallback((fieldName: string, value: any, formData: any) => {
    setValidationState(prev => ({ ...prev, isValidating: true }));

    // Use enhanced validation
    const validation = BookingService.validateBookingData(formData, serviceType, user);
    const fieldError = validation.errors.find(err => err.field === fieldName);

    setValidationState(prev => ({
      ...prev,
      errors: {
        ...prev.errors,
        [fieldName]: fieldError?.message || ''
      },
      touched: {
        ...prev.touched,
        [fieldName]: true
      },
      isValidating: false
    }));

    return !fieldError;
  }, [serviceType, user]);

  const validateAllFields = useCallback((formData: any) => {
    setValidationState(prev => ({ ...prev, isValidating: true }));

    const validation = BookingService.validateBookingData(formData, serviceType, user);

    const errors: Record<string, string> = {};
    const warnings: Record<string, string> = {};

    validation.errors.forEach(error => {
      errors[error.field] = error.message;
    });

    // Mark all fields as touched
    const touched: Record<string, boolean> = {};
    Object.keys(formData).forEach(field => {
      touched[field] = true;
    });

    setValidationState(prev => ({
      ...prev,
      errors,
      warnings,
      touched,
      isValid: validation.isValid,
      isValidating: false
    }));

    return validation.isValid;
  }, [serviceType, user]);

  const clearFieldError = useCallback((fieldName: string) => {
    setValidationState(prev => ({
      ...prev,
      errors: {
        ...prev.errors,
        [fieldName]: ''
      },
      warnings: {
        ...prev.warnings,
        [fieldName]: ''
      }
    }));
  }, []);

  const resetValidation = useCallback(() => {
    setValidationState({
      errors: {},
      warnings: {},
      isValid: false,
      touched: {},
      isValidating: false
    });
  }, []);

  const getFieldError = useCallback((fieldName: string) => {
    return validationState.errors[fieldName] || '';
  }, [validationState.errors]);

  const getFieldWarning = useCallback((fieldName: string) => {
    return validationState.warnings[fieldName] || '';
  }, [validationState.warnings]);

  const isFieldTouched = useCallback((fieldName: string) => {
    return validationState.touched[fieldName] || false;
  }, [validationState.touched]);

  const hasFieldError = useCallback((fieldName: string) => {
    return !!(validationState.errors[fieldName] && validationState.touched[fieldName]);
  }, [validationState.errors, validationState.touched]);

  return {
    validationState,
    validateField,
    validateAllFields,
    clearFieldError,
    resetValidation,
    getFieldError,
    getFieldWarning,
    isFieldTouched,
    hasFieldError,
    errors: validationState.errors,
    warnings: validationState.warnings,
    isValid: validationState.isValid,
    touched: validationState.touched,
    isValidating: validationState.isValidating
  };
}
